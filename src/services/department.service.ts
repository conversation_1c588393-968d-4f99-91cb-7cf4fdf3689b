import { BaseObject } from "src/app/global";
import { BusinessDepartmentList, JobTitleList } from "src/modules/Employees/types/employeeTypes";
import {
  createPseudoLinkAndDownload,
  getFilenameFromContentDisposition,
  ValidFileExtensions,
} from "src/utils/fileUtils";
import { httpClient } from "src/utils/httpClient";
import { apiRegister } from ".";
import { BaseResponse } from "./api_definitions/default.service";
import { AttendanceConfiguration, DefaultConfig } from "./api_definitions/employeeAttendance.service";
import { HierarchyResponse } from "./api_definitions/workRoleHierarchy.service";
import fileuploaderService from "./fileuploader.service";

class DepartmentsService {
  setDepartmentDetails = async (payload: BaseObject[]) => {
    const endpoint = apiRegister.TENANTS.paths["set-department-details"];
    try {
      const response = await httpClient<string>(endpoint, { method: "POST", data: payload });
      return response?.data;
    } catch (_error) {
      return null;
    }
  };

  updateDepartmentDetails = async (payload: BaseObject) => {
    const endpoint = apiRegister.TENANTS.paths["update-department-details"];
    try {
      const response = await httpClient<string>(endpoint, { method: "PATCH", data: payload });
      return response?.data;
    } catch (_error) {
      return null;
    }
  };

  deleteDepartmentDetails = async (payload: BaseObject) => {
    const endpoint = apiRegister.TENANTS.paths["delete-department-details"];
    try {
      const response = await httpClient<string>(endpoint, { method: "DELETE", data: payload });
      return response?.data;
    } catch (_error) {
      return null;
    }
  };

  getDepartmentDetails = async (businessUnit: string) => {
    try {
      const endpoint = apiRegister.TENANTS.paths["get-department-details"].replace(":businessUnit", businessUnit);
      const resp = await httpClient<BaseResponse<BusinessDepartmentList>>(endpoint, { method: "GET" });
      if (resp.data.success === false) {
        return null;
      }
      return resp.data.response;
    } catch (_error) {
      return null;
    }
  };

  getAllDepartments = async () => {
    const endpoint = apiRegister.TENANTS.paths["get-all-departments"];
    const resp = await httpClient<BaseResponse<BaseObject[]>>(endpoint, { method: "GET" });
    return resp.data.response;
  };

  getAllSubDepartments = async () => {
    const endpoint = apiRegister.TENANTS.paths["get-all-sub-departments"];
    const resp = await httpClient<BaseResponse<BaseObject[]>>(endpoint, { method: "GET" });
    return resp.data.response;
  };

  setSubDepartmentDetails = async (payload: BaseObject[]) => {
    const endpoint = apiRegister.TENANTS.paths["set-sub-department-details"];
    try {
      const response = await httpClient<string>(endpoint, { method: "POST", data: payload });
      return response?.data;
    } catch (_error) {
      return null;
    }
  };

  deleteSubDepartmentDetails = async (payload: BaseObject) => {
    const endpoint = apiRegister.TENANTS.paths["delete-sub-department-details"];
    try {
      const response = await httpClient<string>(endpoint, { method: "DELETE", data: payload });
      return response?.data;
    } catch (_error) {
      return null;
    }
  };

  updateSubDepartmentDetails = async (payload: BaseObject) => {
    const endpoint = apiRegister.TENANTS.paths["update-sub-department-details"];
    try {
      const response = await httpClient<string>(endpoint, { method: "PATCH", data: payload });
      return response?.data;
    } catch (_error) {
      return null;
    }
  };

  getSubDepartmentDetails = async (department: string) => {
    const endpoint = apiRegister.TENANTS.paths["get-sub-department-details"].replace(":department", department);
    const resp = await httpClient<BaseResponse<BaseObject[]>>(endpoint, { method: "GET" });
    return resp.data.response;
  };

  getAllTeams = async () => {
    const endpoint = apiRegister.TENANTS.paths["get-all-teams"];
    const resp = await httpClient<BaseResponse<BaseObject[]>>(endpoint, { method: "GET" });
    return resp.data.response;
  };

  setTeamDetails = async (payload: BaseObject[]) => {
    const endpoint = apiRegister.TENANTS.paths["set-team-details"];
    try {
      const response = await httpClient<string>(endpoint, { method: "POST", data: payload });
      return response?.data;
    } catch (_error) {
      return null;
    }
  };

  updateTeamDetails = async (payload: BaseObject) => {
    const endpoint = apiRegister.TENANTS.paths["update-team-details"];
    try {
      const response = await httpClient<string>(endpoint, { method: "PATCH", data: payload });
      return response?.data;
    } catch (_error) {
      return null;
    }
  };

  deleteTeamDetails = async (payload: BaseObject) => {
    const endpoint = apiRegister.TENANTS.paths["delete-team-details"];
    try {
      const response = await httpClient<string>(endpoint, { method: "DELETE", data: payload });
      return response?.data;
    } catch (_error) {
      return null;
    }
  };

  getAllJobFamilies = async () => {
    const endpoint = apiRegister.TENANTS.paths["get-all-job-families"];
    const resp = await httpClient<BaseResponse<BaseObject[]>>(endpoint, { method: "GET" });
    return resp.data.response;
  };

  setJobFamilyDetails = async (payload: BaseObject[]) => {
    const endpoint = apiRegister.TENANTS.paths["set-job-family-details"];
    try {
      const response = await httpClient<string>(endpoint, { method: "POST", data: payload });
      return response?.data;
    } catch (_error) {
      return null;
    }
  };

  updateJobFamilyDetails = async (payload: BaseObject) => {
    const endpoint = apiRegister.TENANTS.paths["update-job-family-details"];
    try {
      const response = await httpClient<string>(endpoint, { method: "PATCH", data: payload });
      return response?.data;
    } catch (_error) {
      return null;
    }
  };

  deleteJobFamilyDetails = async (payload: BaseObject) => {
    const endpoint = apiRegister.TENANTS.paths["delete-job-family-details"];
    try {
      const response = await httpClient<string>(endpoint, { method: "DELETE", data: payload });
      return response?.data;
    } catch (_error) {
      return null;
    }
  };

  getWorkRoleHierarchy = async (tenant_id: string) => {
    try {
      const endpoint = apiRegister.EMPLOYEES.paths["get-work-role-hierarcy"];
      const resp = await httpClient<BaseResponse<HierarchyResponse[]>>(endpoint, {
        method: "GET",
        params: {
          tenant_id: tenant_id,
        },
      });
      if (resp.data.success === false) {
        return null;
      }
      return resp.data.response;
    } catch (_error) {
      return null;
    }
  };

  getJobTitleDetails = async (business_unit: string, department_name: string) => {
    try {
      const endpoint = apiRegister.EMPLOYEES.paths["get-job-title-details"];
      const resp = await httpClient<BaseResponse<JobTitleList>>(endpoint, {
        method: "GET",
        params: {
          business_unit: business_unit,
          department: department_name,
        },
      });
      if (resp.data.success === false) {
        // No search results found
        return null;
      }
      return resp.data.response;
    } catch (_err) {
      return null;
    }
  };

  downloadSampleJobTitleTemplate = async () => {
    try {
      const resp = await httpClient<Blob>(apiRegister.TENANTS.paths["download-job-title-template"], {
        responseType: "blob",
      });

      const [fileName, extention] = (
        getFilenameFromContentDisposition(resp?.headers["content-disposition"]) as string
      ).split(".");

      if (fileName && extention) {
        createPseudoLinkAndDownload(resp.data, `.${extention}` as ValidFileExtensions, fileName);
      }

      return [];
    } catch (_err) {
      return [];
    }
  };

  uploadFile = async (file: File) => {
    const formData = new FormData();
    formData.append("file", file);
    return fileuploaderService.uploadFile(apiRegister.TENANTS.paths["upload-job-titles"], formData);
  };

  getAllJobTitles = async () => {
    const endpoint = apiRegister.TENANTS.paths["get-all-job-titles"];
    const resp = await httpClient<BaseResponse<BaseObject[]>>(endpoint, { method: "GET" });
    return resp.data.response;
  };

  setJobTitleDetails = async (payload: BaseObject[]) => {
    const endpoint = apiRegister.TENANTS.paths["set-job-title-details"];
    try {
      const response = await httpClient<string>(endpoint, { method: "POST", data: payload });
      return response?.data;
    } catch (_error) {
      return null;
    }
  };

  updateJobTitleDetails = async (payload: BaseObject) => {
    const endpoint = apiRegister.TENANTS.paths["update-job-title-details"];
    try {
      const response = await httpClient<string>(endpoint, { method: "PATCH", data: payload });
      return response?.data;
    } catch (_error) {
      return null;
    }
  };

  deleteJobTitleDetails = async (payload: BaseObject) => {
    const endpoint = apiRegister.TENANTS.paths["delete-job-title-details"];
    try {
      const response = await httpClient<string>(endpoint, {
        method: "DELETE",
        data: {
          ...payload,
          work_role: payload?.work_role_name,
        },
      });
      return response?.data;
    } catch (_error) {
      return null;
    }
  };

  setEmployeeIDConfig = async ({ payload }: { payload: Record<string, unknown> }) => {
    const endpoint = apiRegister.TENANTS.paths["set-employee-id-config"];
    try {
      await httpClient<string>(endpoint, { method: "POST", data: payload });
    } catch (_error) {
      return null;
    }
  };

  updateEmployeeIDConfig = async (payload: BaseObject) => {
    const endpoint = apiRegister.TENANTS.paths["update-employee-id-config"];
    try {
      await httpClient<string>(endpoint, { method: "PATCH", data: payload });
    } catch (_error) {
      return null;
    }
  };

  getEmployeeIDConfig = async () => {
    const endpoint = apiRegister.TENANTS.paths["get-employee-id-config"];
    const resp = await httpClient<BaseResponse<BaseObject>>(endpoint, { method: "GET" });
    return resp.data.response;
  };
  getAllWorkRoles = async (tenantId: string) => {
    const endpoint = apiRegister.TENANTS.paths["get-all-work-roles"].replace(":tenantId", tenantId);
    const resp = await httpClient<BaseResponse<BaseObject[]>>(endpoint, { method: "GET" });
    return resp.data.response;
  };

  getAttendanceConfig = async () => {
    const endpoint = apiRegister.TENANTS.paths["get-attendance-config"];
    const resp = await httpClient<BaseResponse<DefaultConfig>>(endpoint, { method: "GET" });
    return resp.data.response;
  };

  getAllAttendanceConfigs = async (code?: string) => {
    const endpoint = apiRegister.TENANTS.paths["get-all-attendance-config"];
    const resp = await httpClient<BaseResponse<AttendanceConfiguration>>(endpoint, { method: "GET", params: { code } });
    return resp.data.response;
  };
  createAttendanceConfig = async (payload: BaseObject) => {
    const endpoint = apiRegister.TENANTS.paths["create-attendance-config"];
    const resp = await httpClient<BaseResponse<BaseObject>>(endpoint, {
      method: "POST",
      data: payload,
    });
    return resp.data.response;
  };
  updateAttendanceConfig = async (payload: BaseObject) => {
    const endpoint = apiRegister.TENANTS.paths["update-attendance-config"];
    const resp = await httpClient<BaseResponse<BaseObject>>(endpoint, {
      method: "PATCH",
      data: payload,
    });
    return resp.data.response;
  };

  getDepartmentEmployeeCount = async () => {
    const endpoint = apiRegister.TENANTS.paths["get-department-employee-count"];
    const resp = await httpClient<BaseResponse<BaseObject>>(endpoint, { method: "GET" });
    return resp.data.response;
  };

  getDepartmentEmployeeData = async () => {
    const endpoint = apiRegister.TENANTS.paths["get-department-employee-data"];
    const resp = await httpClient<BaseResponse<BaseObject>>(endpoint, { method: "GET" });
    return resp.data;
  };
}

export default new DepartmentsService();
