import {
  ContactPage,
  DashboardSharp,
  EventAvailable,
  FollowTheSigns,
  Groups2,
  HailOutlined,
  ImportantDevicesOutlined,
  LogoutRounded,
  MedicalInformation,
  Payment,
  People,
  PermIdentity,
  PersonRemove,
  PunchClock,
  Settings,
  Warehouse,
  WorkHistory,
  Wysiwyg,
} from "@mui/icons-material";
import DescriptionOutlinedIcon from "@mui/icons-material/DescriptionOutlined";
import { FunctionComponent } from "react";
import { CalendarIcon, ReportsIcon, TasksIcon } from "src/assets/icons.svg";
import { PATH_CONFIG } from "src/modules/Routing/config";

export type AccessControl = {
  canRead: boolean;
  canWrite: boolean;
};

export type SubRoute = {
  title: string;
  key: string;
  pathname: string;
  acl?: AccessControl;
  subRoutes?: SubRoute[];
};

export type SideBarMenu = {
  title: string;
  icon?: FunctionComponent;
  pathname: (typeof PATH_CONFIG)[keyof typeof PATH_CONFIG]["path"];
  key: (typeof PATH_CONFIG)[keyof typeof PATH_CONFIG]["key"];
  acl?: AccessControl;
  subRoutes?: SubRoute[];
  isInternal?: boolean;
  redirectURL?: string | null;
};

interface AppConfig {
  sideBarMenus: SideBarMenu[];
  defaultUserRole: string;
}

const employeeScreens = [
  {
    title: "Dashboard",
    icon: DashboardSharp,
    pathname: PATH_CONFIG.HOME.path,
    key: PATH_CONFIG.HOME.key,
    subRoutes: [
      {
        title: "TimeSheets",
        key: PATH_CONFIG.DASHBOARD_TIME_SHEETS.key,
        pathname: PATH_CONFIG.DASHBOARD_TIME_SHEETS.path,
      },
      {
        title: "Holiday",
        key: PATH_CONFIG.DASHBOARD_HOLIDAYS.key,
        pathname: PATH_CONFIG.DASHBOARD_HOLIDAYS.path,
      },
      {
        title: "Calendar",
        key: PATH_CONFIG.DASHBOARD_CALENDAR.key,
        pathname: PATH_CONFIG.DASHBOARD_CALENDAR.path,
      },
      {
        title: "Celebration",
        key: PATH_CONFIG.DASHBOARD_CELEBRATION.key,
        pathname: PATH_CONFIG.DASHBOARD_CELEBRATION.path,
      },
      {
        title: "Todays Team Status",
        key: PATH_CONFIG.DASHBOARD_TODAYS_TEAM_STATUS.key,
        pathname: PATH_CONFIG.DASHBOARD_TODAYS_TEAM_STATUS.path,
      },
      {
        title: "Leave Reports",
        key: PATH_CONFIG.DASHBOARD_LEAVE_REPORTS.key,
        pathname: PATH_CONFIG.DASHBOARD_LEAVE_REPORTS.path,
      },
      {
        title: "Leaves",
        key: PATH_CONFIG.DASHBOARD_LEAVES.key,
        pathname: PATH_CONFIG.DASHBOARD_LEAVES.path,
      },
      {
        title: "Attendance",
        key: PATH_CONFIG.DASHBOARD_ATTENDANCE.key,
        pathname: PATH_CONFIG.DASHBOARD_ATTENDANCE.path,
      },
      {
        title: "New Joiners",
        key: PATH_CONFIG.DASHBOARD_NEW_JOINEES.key,
        pathname: PATH_CONFIG.DASHBOARD_NEW_JOINEES.path,
      },
      {
        title: "Employee Separations",
        key: PATH_CONFIG.DASHBOARD_EMPLOYEE_SEPERATIONS.key,
        pathname: PATH_CONFIG.DASHBOARD_EMPLOYEE_SEPERATIONS.path,
      },
      {
        title: "Department Employee Count",
        key: PATH_CONFIG.DASHBOARD_DEPARTMENT_EMPLOYEE_COUNT.key,
        pathname: PATH_CONFIG.DASHBOARD_DEPARTMENT_EMPLOYEE_COUNT.path,
      },
    ],
  },
  {
    title: "Calendar",
    icon: CalendarIcon,
    pathname: PATH_CONFIG.CALENDAR.path,
    key: PATH_CONFIG.CALENDAR.key,
  },
  {
    title: "Tasks",
    icon: TasksIcon,
    pathname: PATH_CONFIG.TASKS.path,
    key: PATH_CONFIG.TASKS.key,
  },
  {
    title: "Leaves",
    icon: LogoutRounded,
    pathname: PATH_CONFIG.LEAVES.path,
    key: PATH_CONFIG.LEAVES.key,
  },
  {
    title: "Attendance",
    icon: EventAvailable,
    pathname: PATH_CONFIG.EMPLOYEE_ATTENDANCE.path,
    key: PATH_CONFIG.EMPLOYEE_ATTENDANCE.key,
  },
  {
    title: "Documents",
    icon: DescriptionOutlinedIcon,
    pathname: PATH_CONFIG.EMPLOYEE_DOCUMENTS.path,
    key: PATH_CONFIG.EMPLOYEE_DOCUMENTS.key,
  },
  {
    title: "Performance",
    icon: ImportantDevicesOutlined,
    pathname: PATH_CONFIG.PEFRORMANCE_MANAGEMENT.path,
    key: PATH_CONFIG.PEFRORMANCE_MANAGEMENT.key,
  },
  {
    title: "My Profile",
    icon: PermIdentity,
    pathname: PATH_CONFIG.PROFILE.path,
    key: PATH_CONFIG.PROFILE.key,
  },
  {
    title: "Separations",
    icon: PersonRemove,
    pathname: PATH_CONFIG.EMPLOYEE_SEPERATIONS.path,
    key: PATH_CONFIG.EMPLOYEE_SEPERATIONS.key,
  },
  {
    title: "Separation",
    icon: PersonRemove,
    pathname: PATH_CONFIG.EMPLOYEE_SEPERATION.path,
    key: PATH_CONFIG.EMPLOYEE_SEPERATION.key,
  },
  {
    title: "Payroll",
    icon: Payment,
    pathname: PATH_CONFIG.PAYROLL_INTEGRATION.path,
    key: PATH_CONFIG.PAYROLL_INTEGRATION.key,
  },
  {
    title: "Insurance",
    icon: MedicalInformation,
    pathname: PATH_CONFIG.INSURANCE_INTEGRATION.path,
    key: PATH_CONFIG.INSURANCE_INTEGRATION.key,
  },
  {
    title: "Project Tracking",
    icon: TasksIcon,
    pathname: PATH_CONFIG.PROJECT_TRACKING.path,
    key: PATH_CONFIG.PROJECT_TRACKING.key,
  },
  {
    title: "Time Log",
    icon: WorkHistory,
    key: PATH_CONFIG.TIMESHEET_TRACKING.key,
    pathname: PATH_CONFIG.TIMESHEET_TRACKING.path,
  },
];

const developerScreens = [
  {
    title: "Screen Management",
    icon: Wysiwyg,
    pathname: PATH_CONFIG.SCREEN_MANAGEMENT.path,
    key: PATH_CONFIG.SCREEN_MANAGEMENT.key,
  },
];

const tenantAdminScreens = [
  {
    title: "Tenants",
    icon: Warehouse,
    pathname: PATH_CONFIG.TENANTS.path,
    key: PATH_CONFIG.TENANTS.key,
    subRoutes: [
      {
        title: "Edit Tenant",
        key: PATH_CONFIG.TENANTS_EDIT.key,
        pathname: PATH_CONFIG.TENANTS_EDIT.path,
      },
      {
        title: "Tenants",
        key: PATH_CONFIG.TENANTS_LIST.key,
        pathname: PATH_CONFIG.TENANTS_LIST.path,
      },
    ],
  },
];

const hrAdminScreens = [
  {
    title: "Employees",
    icon: People,
    pathname: PATH_CONFIG.EMPLOYEES.path,
    key: PATH_CONFIG.EMPLOYEES.key,
  },
  {
    title: "New Joiners",
    icon: HailOutlined,
    pathname: PATH_CONFIG.NEWJOINEES.path,
    key: PATH_CONFIG.NEWJOINEES.key,
  },
  {
    title: "Time Sheets",
    icon: PunchClock,
    pathname: PATH_CONFIG.TIME_SHEETS.path,
    key: PATH_CONFIG.TIME_SHEETS.key,
  },
  {
    title: "Leads",
    icon: ContactPage,
    pathname: PATH_CONFIG.LEADS.path,
    key: PATH_CONFIG.LEADS.key,
  },
  {
    title: "Reports",
    icon: ReportsIcon,
    pathname: PATH_CONFIG.REPORTS.path,
    key: PATH_CONFIG.REPORTS.key,
  },
  {
    title: "Users",
    icon: Groups2,
    pathname: PATH_CONFIG.USERS.path,
    key: PATH_CONFIG.USERS.key,
  },
  {
    title: "Offboarding",
    icon: FollowTheSigns,
    pathname: PATH_CONFIG.EMPLOYEE_OFFBOARDING.path,
    key: PATH_CONFIG.EMPLOYEE_OFFBOARDING.key,
  },
  {
    title: "Client Projects",
    key: PATH_CONFIG.TIMESHEET_MANAGEMENT.key,
    icon: WorkHistory,
    pathname: PATH_CONFIG.TIMESHEET_MANAGEMENT.path,
  },
];

const commonSettings = [
  {
    title: "Settings",
    icon: Settings,
    pathname: PATH_CONFIG.SETTINGS.path,
    key: PATH_CONFIG.SETTINGS.key,
    subRoutes: [
      {
        title: "Organisations",
        key: PATH_CONFIG.ORGANISATIONS.key,
        pathname: PATH_CONFIG.ORGANISATIONS.key,
      },
      {
        title: "Business Units",
        key: PATH_CONFIG.BUSINESS_UNIT.key,
        pathname: PATH_CONFIG.BUSINESS_UNIT.path,
      },
      {
        title: "Departments",
        key: PATH_CONFIG.DEPARTMENT.key,
        pathname: PATH_CONFIG.DEPARTMENT.path,
      },
      {
        title: "Sub Departments",
        key: PATH_CONFIG.SUB_DEPARTMENT.key,
        pathname: PATH_CONFIG.SUB_DEPARTMENT.path,
      },
      {
        title: "Job Families",
        key: PATH_CONFIG.JOB_FAMILY.key,
        pathname: PATH_CONFIG.JOB_FAMILY.path,
      },
      {
        title: "Job Titles",
        key: PATH_CONFIG.JOB_TITLE.key,
        pathname: PATH_CONFIG.JOB_TITLE.path,
      },

      {
        title: "Teams",
        key: PATH_CONFIG.TEAM.key,
        pathname: PATH_CONFIG.TEAM.path,
      },
      {
        title: "Configuration",
        key: PATH_CONFIG.CONFIGURATION.key,
        pathname: PATH_CONFIG.CONFIGURATION.path,
        subRoutes: [
          {
            title: "Employee",
            key: PATH_CONFIG.EMPLOYEE_ID_CONFIGURATION.key,
            pathname: PATH_CONFIG.EMPLOYEE_ID_CONFIGURATION.path,
          },
          {
            title: "Attendance",
            key: PATH_CONFIG.ATTENDANCE_CONFIGURATION.key,
            pathname: PATH_CONFIG.ATTENDANCE_CONFIGURATION.path,
          },
        ],
      },
      {
        title: "Role Management",
        key: PATH_CONFIG.ROLE_MANAGEMENT.key,
        pathname: PATH_CONFIG.ROLE_MANAGEMENT.path,
        subRoutes: [
          {
            title: "All Roles",
            key: PATH_CONFIG.ALL_ROLES.key,
            pathname: PATH_CONFIG.ALL_ROLES.path,
          },
          {
            title: "Manage Roles",
            key: PATH_CONFIG.MANAGE_ROLES.key,
            pathname: PATH_CONFIG.MANAGE_ROLES.path,
          },
          {
            title: "Role Hierarchies",
            key: PATH_CONFIG.MANAGE_HIERARCHIAL_ROLES.key,
            pathname: PATH_CONFIG.MANAGE_HIERARCHIAL_ROLES.path,
          },
        ],
      },
      {
        title: "Cost Center",
        key: PATH_CONFIG.COST_CENTER.key,
        pathname: PATH_CONFIG.COST_CENTER.path,
      },
      {
        title: "Work Roles",
        key: PATH_CONFIG.WORK_ROLE.key,
        pathname: PATH_CONFIG.WORK_ROLE.path,
      },
      {
        title: "Leave Management",
        key: PATH_CONFIG.LEAVE_MANAGEMENT.key,
        pathname: PATH_CONFIG.LEAVE_MANAGEMENT.path,
        subRoutes: [
          {
            title: "Holidays",
            key: PATH_CONFIG.LEAVE_MANAGEMENT_HOLIDAYS.key,
            pathname: PATH_CONFIG.LEAVE_MANAGEMENT_HOLIDAYS.path,
          },
          {
            title: "Leave Types",
            key: PATH_CONFIG.LEAVE_MANAGEMENT_LEAVE_TYPES.key,
            pathname: PATH_CONFIG.LEAVE_MANAGEMENT_LEAVE_TYPES.path,
          },
          {
            title: "Leave Policies",
            key: PATH_CONFIG.LEAVE_MANAGEMENT_POLICIES.key,
            pathname: PATH_CONFIG.LEAVE_MANAGEMENT_POLICIES.path,
          },
        ],
      },
      {
        title: "Performance Management",
        key: PATH_CONFIG.SETTINGS_PERFORMANCEMANAGEMENT.key,
        pathname: PATH_CONFIG.SETTINGS_PERFORMANCEMANAGEMENT.path,
        subRoutes: [
          {
            title: "Review Cycles",
            key: PATH_CONFIG.SETTINGS_PERFORMANCEMANAGEMENT_REVIEWCYCLES.key,
            pathname: PATH_CONFIG.SETTINGS_PERFORMANCEMANAGEMENT_REVIEWCYCLES.path,
          },
          {
            title: "Ratings",
            key: PATH_CONFIG.SETTINGS_PERFORMANCEMANAGEMENT_RATINGSCONFIG.key,
            pathname: PATH_CONFIG.SETTINGS_PERFORMANCEMANAGEMENT_RATINGSCONFIG.path,
          },
        ],
      },
      {
        title: "Offboarding",
        key: PATH_CONFIG.SETTINGS_OFFBOARDING.key,
        pathname: PATH_CONFIG.SETTINGS_OFFBOARDING.path,
      },
      {
        title: "Onboarding",
        key: PATH_CONFIG.SETTINGS_PROBATIONS.key,
        pathname: PATH_CONFIG.SETTINGS_PROBATIONS.path,
      },
      {
        title: "Compensation Components",
        key: PATH_CONFIG.PAYROLL_COMPENSATION_COMPONENTS.key,
        pathname: PATH_CONFIG.PAYROLL_COMPENSATION_COMPONENTS.path,
      },
      {
        title: "Compensation Templates",
        key: PATH_CONFIG.PAYROLL_COMPENSATION_TEMPLATES.key,
        pathname: PATH_CONFIG.PAYROLL_COMPENSATION_TEMPLATES.path,
      },
      {
        title: "Statutory Components",
        key: PATH_CONFIG.PAYROLL_STATUTORY_COMPONENTS.key,
        pathname: PATH_CONFIG.PAYROLL_STATUTORY_COMPONENTS.path,
      },
      {
        title: "Pay Schedule",
        key: PATH_CONFIG.PAYROLL_PAY_SCHEDULE.key,
        pathname: PATH_CONFIG.PAYROLL_PAY_SCHEDULE.path,
      },
    ],
  },
];

const compensationPayroll = [
  {
    title: "Payroll",
    icon: Payment,
    pathname: PATH_CONFIG.PAYROLL.path,
    key: PATH_CONFIG.PAYROLL.key,
    subRoutes: [
      {
        title: "Dashboard",
        key: PATH_CONFIG.PAYROLL_DASHBOARD.key,
        pathname: PATH_CONFIG.PAYROLL_DASHBOARD.path,
      },
      {
        title: "Pay Runs",
        key: PATH_CONFIG.PAYROLL_PAYRUNS.key,
        pathname: PATH_CONFIG.PAYROLL_PAYRUNS.path,
      },
      {
        title: "Approvals",
        key: PATH_CONFIG.PAYROLL_APPROVALS.key,
        pathname: PATH_CONFIG.PAYROLL_APPROVALS.path,
      },
    ],
  },
];

const internalScreens = [
  {
    title: "Employee Profile",
    pathname: PATH_CONFIG.EMPLOYEE_PROFILE.path,
    key: PATH_CONFIG.EMPLOYEE_PROFILE.key,
    isInternal: true,
  },
];

const appConfig: AppConfig = {
  sideBarMenus: [
    ...employeeScreens,
    ...developerScreens,
    ...tenantAdminScreens,
    ...hrAdminScreens,
    ...commonSettings,
    ...internalScreens,
    ...compensationPayroll,
  ],
  defaultUserRole: "EMPLOYEE",
};

export default appConfig;
