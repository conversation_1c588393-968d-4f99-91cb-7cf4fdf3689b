import { Card, Paper } from "@mui/material";
import React from "react";

interface PreviewCardProps {
  children: JSX.Element;
  overrideHeight?: number;
}

const PreviewCard: React.FC<PreviewCardProps> = ({ children, overrideHeight }) => {
  return (
    <Card
      component={Paper}
      elevation={4}
      sx={{
        padding: "20px 15px",
        borderRadius: 4,
        height: overrideHeight || 350,
      }}
    >
      {children}
    </Card>
  );
};

export default PreviewCard;
