import { lazy } from "react";

const LazyAttendanceQuickView = lazy(() => import("./Attendance"));
const LazyHolidaysQuickView = lazy(() => import("./Holidays"));
const LazyCalendarQuickView = lazy(() => import("./Calendar"));
const LazyTeamStatusQuckView = lazy(() => import("./TeamStatus"));
const LazyEventsView = lazy(() => import("./Events"));
const LazyEmployeeAttendanceView = lazy(() => import("./EmployeeAttendance"));
const LazyEmployeeLeaveReportsView = lazy(() => import("./LeaveReports"));
const LazyEmployeeLeavesView = lazy(() => import("./Leaves"));
const LazyNewJoinersView = lazy(() => import("./NewJoiners"));
const LazyEmployeeSeperations = lazy(() => import("./EmployeeSeperations"));
const LazyDepartmentEmployeeChartView = lazy(() => import("./DepartmentEmployeeChart"));
export {
  LazyAttendanceQuickView,
  LazyHolidaysQuickView,
  LazyCalendarQuickView,
  LazyTeamStatusQuckView,
  LazyEventsView,
  LazyEmployeeAttendanceView,
  LazyEmployeeLeaveReportsView,
  LazyEmployeeLeavesView,
  LazyNewJoinersView,
  LazyEmployeeSeperations,
  LazyDepartmentEmployeeChartView,
};
