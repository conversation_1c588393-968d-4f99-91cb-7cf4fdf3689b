import { <PERSON><PERSON>, <PERSON>, CircularProgress, <PERSON>, <PERSON>ack, Typography } from "@mui/material";
import { Bar<PERSON><PERSON> } from "@mui/x-charts/BarChart";
import { useQuery } from "@tanstack/react-query";
import React, { useState } from "react";
import Modal from "src/modules/Common/Modal/Modal";
import DepartmentEmployee from "src/modules/PerformanceManagement/components/DepartmentEmployees";
import { ResourceAllocationData } from "src/modules/PerformanceManagement/ResourceAllocationTypes";
import departmentService from "src/services/department.service";
import { HeaderContainer } from "./style";

interface DepartmentData {
  department_name: string;
  employee_count: number;
}

interface DepartmentEmployeeResponse {
  totalEmployees: string;
  departments: DepartmentData[];
}

interface Props {
  data?: DepartmentEmployeeResponse;
  isLoading?: boolean;
}

const DEPARTMENT_COLORS = {
  PRIMARY: "#87bc45",
  SECONDARY: "#63bff0",
  TERTIARY: "#de6e56",
  QUATERNARY: "#f4a261",
  QUINARY: "#e76f51",
};


const typography = {
  titleText: "Department Wise Head Count",
  buttonText: "View Hierarchy",
};

const DepartmentEmployeeChart = ({ data, isLoading = false }: Props) => {
  if (isLoading) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "500px",
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  if (!data || !data.departments || data.departments.length === 0) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          height: "500px",
          color: "text.secondary",
          fontSize: "1rem",
        }}
      >
        No department data available
      </Box>
    );
  }

  const chartData = data.departments.map((dept) => ({
    department: dept.department_name.length > 12
      ? dept.department_name.substring(0, 12) + '...'
      : dept.department_name,
    fullDepartmentName: dept.department_name,
    employeeCount: dept.employee_count,
  }));

  return (
    <Stack sx={{ height: '100%', overflow: 'hidden' }}>
      <Alert>
        <Typography variant="body2" fontWeight={600}>
          Total Employees: {data.totalEmployees}
        </Typography></Alert>
      <BarChart
        series={[
          {
            data: chartData.map((item) => item.employeeCount),
            label: "Head Count",
            color: DEPARTMENT_COLORS.PRIMARY,
            valueFormatter: (value) => `${value} employees`,
          },
        ]}
        xAxis={[
          {
            data: chartData.map((item) => item.department),
            scaleType: "band",
            // label: "Departments",
            labelStyle: {
              fontSize: 12,
              transform: "translateY(36px)",
            },
            tickLabelStyle: {
              angle: 0,
              textAnchor: "middle",
              fontSize: 10,
            },
            valueFormatter: (value, context) => {
              // For tooltips, show full department name
              if (context?.location === 'tooltip') {
                const dataIndex = chartData.findIndex(item => item.department === value);
                return dataIndex >= 0 ? data.departments[dataIndex].department_name : value;
              }
              // For axis labels, show abbreviated name
              return value;
            },
          },
        ]}
        yAxis={[
          {
            // label: "Employee Count",
            tickMinStep: 1,
            min: 0,
          },
        ]}
        height={220}
        margin={{
          left: 50,
          right: 30,
          top: 30,
          bottom: 50,
        }}

        slotProps={{
          legend: {
            hidden: true,
          },
        }}
      />
    </Stack>
  );
};

// Converter function to transform employeeDepartmentData to ResourceAllocationData
const convertDepartmentEmployeeData = (data: any): ResourceAllocationData => {
  const convertEmployeeToMappedResource = (
    employee: any,
    goalObjectiveId: string,
    departmentName: string = "Unknown",
  ): any => ({
    estimated_weightage: 100, // Default weightage
    goal_objective_id: goalObjectiveId,
    employee_code: employee.employee_code,
    employee: {
      employee_code: employee.employee_code,
      display_name: employee.display_name,
      display_pic:
        employee.display_pic ||
        `https://ui-avatars.com/api/?name=${encodeURIComponent(employee.display_name)}&background=random`,
      job_title: employee.job_title,
      email: `${employee.employee_code.toLowerCase()}@company.com`,
      gender: "Unknown",
      department: departmentName,
      business_unit: "Technology",
      tenure: "Unknown",
      number_of_reportees: employee.reportees?.length || 0,
    },
    mapped_resources:
      employee.reportees?.map((reportee: any, index: number) =>
        convertEmployeeToMappedResource(reportee, `${goalObjectiveId}-${index}`, departmentName),
      ) || [],
  });

  // Process all departments that have department heads
  const departmentsWithHeads = data.response?.filter((dept: any) => dept.department_heads?.length > 0) || [];

  if (departmentsWithHeads.length === 0) {
    return {
      objective_resource_map: [],
    };
  }

  return {
    objective_resource_map: departmentsWithHeads.map((dept: any, deptIndex: number) => ({
      goal_objective_id: `department-${deptIndex}`,
      goal_objective_title: dept.department_name,
      estimated_weightage: 100,
      mapped_resources: dept.department_heads.map((head: any, headIndex: number) =>
        convertEmployeeToMappedResource(head, `dept-${deptIndex}-head-${headIndex}`, dept.department_name),
      ),
    })),
  };
};

const DepartmentEmployeeChartComponent = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { data, isLoading: isDepartmentEmployeeCountLoading } = useQuery({
    queryKey: ["department-employee-chart"],
    queryFn: () => departmentService.getDepartmentEmployeeCount(),
  });
  const totalEmployees =
    (data as unknown as DepartmentData[])?.reduce((a: number, b: DepartmentData) => a + b.employee_count, 0) || 0;

  const { data: departmentEmployeeData, isLoading: isDepartmentEmployeeDataLoading } = useQuery({
    queryKey: ["department-employee-data"],
    queryFn: () => departmentService.getDepartmentEmployeeData(),
  });

  if (isDepartmentEmployeeDataLoading || isDepartmentEmployeeCountLoading) {
    return <CircularProgress />;
  }

  const onViewAllClick = () => {
    setIsModalOpen(true);
  };

  return (
    <Stack spacing={2}>
      <HeaderContainer>
        <Typography>{typography.titleText}</Typography>
        <Link sx={{ cursor: "pointer", textDecoration: "none" }} onClick={onViewAllClick}>
          <Typography fontSize={14} color="#1F7ABF">
            {typography.buttonText}
          </Typography>
        </Link>
      </HeaderContainer>
      <DepartmentEmployeeChart
        data={{
          totalEmployees: totalEmployees.toString(),
          departments: data as unknown as DepartmentData[],
        }}
      />
      <Modal
        title={typography.titleText}
        showBackButton={true}
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
        }}
      >
        <Box>
          <DepartmentEmployee
            resourceData={convertDepartmentEmployeeData(departmentEmployeeData)}
            onBack={() => {
              setIsModalOpen(false);
            }}
          />
        </Box>
      </Modal>
    </Stack>
  );
};
export default DepartmentEmployeeChartComponent;
