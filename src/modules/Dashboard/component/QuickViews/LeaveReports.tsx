import { Box, Chip, Divider, Paper, Typography } from "@mui/material";
import { useQuery } from "@tanstack/react-query";
import React, { useMemo } from "react";
import { useAppSelector } from "src/customHooks/useAppSelector";
import ScrollableBox from "src/modules/Common/Container/ScrollableBox";
import leavesService from "src/services/leaves.service";
import NoData from "./components/NoDataScreens/NoData";
import { HeaderContainer } from "./style";

const LeaveReports = () => {
  const { userDetails } = useAppSelector((state) => state.userManagement);

  const { data } = useQuery({
    queryKey: ["get-leave-summary"],
    queryFn: async () => leavesService.getLeaveSummary(),
    retryOnMount: false,
    refetchOnWindowFocus: false,
    enabled: userDetails?.organisations?.length > 0,
  });

  const totalLeaves = useMemo(() => {
    const total = data?.filter((leaveSummary) => leaveSummary.paid)?.reduce((a, b) => a + b.noOfLeaves, 0) || 0;
    return Math.round(total * 100) / 100; // Round to 1 decimal place
  }, [data]);

  if (data?.length === 0) {
    return <NoData title="No leaves assigned yet" />;
  }

  return (
    <Box display="flex" flexDirection="column" gap={2}>
      <HeaderContainer>
        <Typography>Leave Reports</Typography>
        <Box display="flex" gap={1} alignItems="center">
          <Typography fontSize={14} fontWeight={600}>
            Total: {totalLeaves || 0}
          </Typography>
        </Box>
      </HeaderContainer>
      <Divider />
      <ScrollableBox maxHeight={250}>
        <Paper elevation={0}>
          {data?.map((leaveSummary) => (
            <Box
              key={leaveSummary.leaveType}
              display="flex"
              justifyContent="space-between"
              alignItems="center"
              padding="10px 0px"
            >
              <Box display="flex" alignItems="center" gap={1}>
                <Typography fontSize={14}>{leaveSummary.leaveType}</Typography>
                {!leaveSummary.paid && (
                  <Chip
                    label={"Unpaid"}
                    size="small"
                    color={"error"}
                    variant="outlined"
                    sx={{ fontSize: 10, height: 20 }}
                  />
                )}
              </Box>
              <Typography fontSize={14} fontWeight={600}>
                {leaveSummary.noOfLeaves}
              </Typography>
            </Box>
          ))}
        </Paper>
      </ScrollableBox>
    </Box>
  );
};

export default LeaveReports;
