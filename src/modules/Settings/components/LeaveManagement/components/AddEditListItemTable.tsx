import { InfoOutlined } from "@mui/icons-material";
import {
  Box,
  Checkbox,
  InputLabel,
  ListItemText,
  MenuItem,
  Select,
  Switch,
  styled,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import { MRT_ColumnDef } from "material-react-table";
import React, { useMemo } from "react";

import DataTable from "src/modules/Common/Table/DataTable";
import { LeaveAttribute } from "src/services/api_definitions/leaveManagement.service";

const CustomInputLabel = styled(InputLabel)(() => ({
  fontSize: 14,
}));

type DropdownOption = {
  accrualFrequency: string[];
  allocationMethod: string[];
};

interface AddEditLeavePolicyListItemProps {
  leaveTypes: LeaveAttribute[]; // multiple items => multiple rows
  handleFormChange: (key: string, value: unknown, index: number) => void;
  dropdownOptions: DropdownOption;
  isLeaveEncashmentEnabled?: boolean;
}

const translations = {
  leaveType: "Leave Type",
  enabled: "Enabled",
  days: "No. of Days",
  allocationMethod: "Method",
  accrualFrequency: "Frequency",
  carryForwardAllowed: "Allowed",
  carryForwardLimit: "Limit",
  minimumAllowed: "Minimum Allowed",
  maximumAllowed: "Maximum Allowed",
  weekendIncluded: "Include Weekend",
  holidayIncluded: "Include Holiday",
  halfDayAllowed: "Half Day Allowed",
  oneTime: "One Time Only",
  allowedOnProbation: "Allowed on Probation",
  allowedOnNotice: "Allowed on Notice",
  encashable: "Encashable",
  allocationsAnnualHeader: "Allocation (Annual)",
  carryForward: "Carry Forward",
  autoApprove: "Auto Approve",
  inLabel: "In",
  daysLabel: "days",
  autoApproveTooltip: "Automatically approve this leave type if no action is taken within the selected days.",
};

const useLeavePolicyColumns = (
  dropdownOptions: DropdownOption,
  handleFormChange: (key: string, value: unknown, index: number) => void,
  isLeaveEncashmentEnabled?: boolean,
) => {
  return useMemo<MRT_ColumnDef<LeaveAttribute>[]>(
    () => [
      // 1) Leave Type
      {
        accessorKey: "leaveType",
        header: translations.leaveType,
        size: 300,
        Cell: ({ row }) => {
          const rowData = row.original;
          return (
            <Typography fontWeight="600" fontSize={16}>
              {rowData.leaveType}
            </Typography>
          );
        },
      },
      {
        accessorKey: "enabled",
        size: 80,
        header: "",
        Cell: ({ row }) => {
          const { index, original: rowData } = row;
          return (
            <Box width={50}>
              <Switch
                checked={rowData.enabled}
                id="enabled"
                onChange={(ev, checked) => handleFormChange(ev.target.id, checked, index)}
              />
            </Box>
          );
        },
      },
      {
        header: translations.allocationsAnnualHeader,
        muiTableHeadCellProps: {
          align: "center",
        },
        Cell: ({ row }) => {
          const { index, original: rowData } = row;

          if (!rowData.enabled) return null;

          return (
            <Box
              display="flex"
              justifyContent={rowData?.allocationMethod === "Pro-Rated Accrued" ? "space-around" : "flex-start"}
              alignItems="center"
              gap={2}
            >
              {/* No. of Days */}
              <Box display="flex" flexDirection="row" alignItems="center" gap={1}>
                <CustomInputLabel required htmlFor="days" sx={{ margin: 0, fontSize: 13 }}>
                  {translations.days}
                </CustomInputLabel>
                <TextField
                  required
                  id="days"
                  type="number"
                  value={rowData.days}
                  disabled={!rowData.enabled}
                  onChange={(ev) => handleFormChange(ev.target.id, ev.target.value, index)}
                  slotProps={{
                    input: {
                      sx: {
                        width: 50,
                        height: 30,
                        fontSize: 12,
                      },
                    },
                  }}
                />
              </Box>
              {/* Accrual Frequency */}
              <Box display="flex" flexDirection="row" alignItems="center" gap={1}>
                <CustomInputLabel htmlFor="allocationMethod" required sx={{ margin: 0, fontSize: 13 }}>
                  {translations.allocationMethod}
                </CustomInputLabel>
                <Select
                  id="allocationMethod"
                  value={rowData.allocationMethod ?? ""}
                  disabled={!rowData.enabled}
                  size="small"
                  sx={{
                    width: 200,
                    height: 30,
                  }}
                  onChange={(ev) => handleFormChange("allocationMethod", ev.target.value, index)}
                >
                  {dropdownOptions?.allocationMethod?.map((method) => (
                    <MenuItem key={method} value={method}>
                      <ListItemText>{method}</ListItemText>
                    </MenuItem>
                  ))}
                </Select>
              </Box>
              {/* Accrual Frequency */}
              {rowData.allocationMethod === "Pro-Rated Accrued" && (
                <Box display="flex" flexDirection="row" alignItems="center" gap={1}>
                  <CustomInputLabel
                    htmlFor="accrualFrequency"
                    required={rowData.allocationMethod === "Pro-Rated Accrued"}
                    sx={{ margin: 0, fontSize: 13 }}
                  >
                    {translations.accrualFrequency}
                  </CustomInputLabel>
                  <Select
                    id="accrualFrequency"
                    value={rowData.accrualFrequency ?? ""}
                    disabled={!rowData.enabled}
                    size="small"
                    sx={{
                      width: 130,
                      height: 30,
                    }}
                    onChange={(ev) => handleFormChange("accrualFrequency", ev.target.value, index)}
                  >
                    {dropdownOptions?.accrualFrequency?.map((frequency) => (
                      <MenuItem key={frequency} value={frequency}>
                        <ListItemText>{frequency}</ListItemText>
                      </MenuItem>
                    ))}
                  </Select>
                </Box>
              )}
            </Box>
          );
        },
      },
      // Auto Approve (per leave)
      {
        header: translations.autoApprove,
        Header: ({ table }) =>
          (
            <Box display="flex" alignItems="center" gap={0.5} {...table}>
              {translations.autoApprove}
              <Tooltip placement="top" title={translations.autoApproveTooltip}>
                <InfoOutlined fontSize="small" sx={{ color: "#888" }} />
              </Tooltip>
            </Box>
          ) as unknown as string,
        muiTableHeadCellProps: { align: "center" },
        muiTableBodyCellProps: { align: "center" },
        Cell: ({ row }) => {
          const { index, original: rowData } = row;
          if (!rowData.enabled) return null;
          const isChecked = (rowData.autoApproveDays ?? null) != null;
          return (
            <Box display="flex" alignItems="center" gap={1}>
              <Checkbox
                id="autoApproveEnabled"
                checked={isChecked}
                disabled={!rowData.enabled}
                onChange={(_ev, checked) =>
                  handleFormChange("autoApproveDays", checked ? (rowData.autoApproveDays ?? 1) : null, index)
                }
              />
              <Typography>{translations.inLabel}</Typography>
              <Select
                id="autoApproveDays"
                size="small"
                sx={{ width: 80, height: 30 }}
                disabled={!isChecked}
                value={String(rowData.autoApproveDays ?? "")}
                displayEmpty
                renderValue={(selected) => {
                  if (!selected) return <Typography color="gray">{""}</Typography>;
                  return selected as React.ReactNode;
                }}
                onChange={(ev) => handleFormChange("autoApproveDays", Number(ev.target.value), index)}
              >
                {[1, 2, 3, 4, 5].map((n) => (
                  <MenuItem key={n} value={String(n)}>
                    {n}
                  </MenuItem>
                ))}
              </Select>
              <Typography>{translations.daysLabel}</Typography>
            </Box>
          );
        },
      },
      // 5) Carry Forward => hide if not enabled
      {
        header: translations.carryForward,
        muiTableHeadCellProps: {
          align: "center",
        },
        muiTableBodyCellProps: {
          align: "center",
        },
        Cell: ({ row }) => {
          const { index, original: rowData } = row;

          if (!rowData.enabled) return null;

          return (
            <Box display="flex" justifyContent="space-around" alignItems="center" gap={1}>
              <Box display="flex" flexDirection="row" alignItems="center">
                <CustomInputLabel
                  htmlFor="carryForwardAllowed"
                  disabled={!rowData.enabled}
                  sx={{ margin: 0, fontSize: 13 }}
                >
                  {translations.carryForwardAllowed}
                </CustomInputLabel>
                <Checkbox
                  id="carryForwardAllowed"
                  checked={rowData.carryForwardAllowed}
                  disabled={!rowData.enabled}
                  onChange={(ev, checked) => handleFormChange(ev.target.id, checked, index)}
                  sx={{ justifySelf: "start" }}
                />
              </Box>

              <Box display="flex" flexDirection="row" alignItems="center" gap={1}>
                <CustomInputLabel
                  required={rowData.carryForwardAllowed}
                  htmlFor="carryForwardLimit"
                  disabled={!rowData.enabled}
                  sx={{ margin: 0, fontSize: 13 }}
                >
                  {translations.carryForwardLimit}
                </CustomInputLabel>
                <TextField
                  id="carryForwardLimit"
                  type="number"
                  value={rowData.carryForwardLimit}
                  size="small"
                  onChange={(ev) => handleFormChange(ev.target.id, ev.target.value, index)}
                  slotProps={{
                    input: {
                      sx: {
                        width: 50,
                        height: 30,
                        fontSize: 12,
                      },
                    },
                  }}
                  disabled={!rowData.enabled || !rowData.carryForwardAllowed}
                />
              </Box>
            </Box>
          );
        },
      },
      // 6) Minimum Allowed => hide if not enabled
      {
        accessorKey: "minimumAllowed",
        muiTableHeadCellProps: {
          align: "center",
        },
        muiTableBodyCellProps: {
          align: "center",
        },
        header: translations.minimumAllowed,
        Cell: ({ row }) => {
          const { index, original: rowData } = row;

          if (!rowData.enabled) return null;

          return (
            <TextField
              id="minimumAllowed"
              type="number"
              value={rowData.minimumAllowed}
              disabled={!rowData.enabled}
              onChange={(ev) => handleFormChange(ev.target.id, ev.target.value, index)}
              slotProps={{
                input: {
                  sx: {
                    width: 50,
                    height: 30,
                    fontSize: 12,
                  },
                },
              }}
            />
          );
        },
      },
      // 7) Maximum Allowed => hide if not enabled
      {
        accessorKey: "maximumAllowed",
        muiTableHeadCellProps: {
          align: "center",
        },
        muiTableBodyCellProps: {
          align: "center",
        },
        header: translations.maximumAllowed,
        Cell: ({ row }) => {
          const { index, original: rowData } = row;

          if (!rowData.enabled) return null;

          return (
            <TextField
              id="maximumAllowed"
              type="number"
              value={rowData.maximumAllowed}
              disabled={!rowData.enabled}
              onChange={(ev) => handleFormChange(ev.target.id, ev.target.value, index)}
              slotProps={{
                input: {
                  sx: {
                    width: 50,
                    height: 30,
                    fontSize: 12,
                  },
                },
              }}
            />
          );
        },
      },
      // 8) Include Weekend => hide if not enabled
      {
        accessorKey: "weekendIncluded",
        muiTableHeadCellProps: {
          align: "center",
        },
        muiTableBodyCellProps: {
          align: "center",
        },
        header: translations.weekendIncluded,
        Cell: ({ row }) => {
          const { index, original: rowData } = row;

          if (!rowData.enabled) return null;

          return (
            <Checkbox
              id="weekendIncluded"
              checked={rowData.weekendIncluded}
              disabled={!rowData.enabled}
              onChange={(ev, checked) => handleFormChange(ev.target.id, checked, index)}
            />
          );
        },
      },
      // 9) Include Holiday => hide if not enabled
      {
        accessorKey: "holidayIncluded",
        muiTableHeadCellProps: {
          align: "center",
        },
        muiTableBodyCellProps: {
          align: "center",
        },
        header: translations.holidayIncluded,
        Cell: ({ row }) => {
          const { index, original: rowData } = row;

          if (!rowData.enabled) return null;

          return (
            <Checkbox
              id="holidayIncluded"
              checked={rowData.holidayIncluded}
              disabled={!rowData.enabled}
              onChange={(ev, checked) => handleFormChange(ev.target.id, checked, index)}
            />
          );
        },
      },
      // 10) Half Day Allowed => hide if not enabled
      {
        accessorKey: "halfDayAllowed",
        muiTableHeadCellProps: {
          align: "center",
        },
        muiTableBodyCellProps: {
          align: "center",
        },
        header: translations.halfDayAllowed,
        Cell: ({ row }) => {
          const { index, original: rowData } = row;

          if (!rowData.enabled) return null;

          return (
            <Checkbox
              id="halfDayAllowed"
              checked={rowData.halfDayAllowed}
              disabled={!rowData.enabled}
              onChange={(ev, checked) => handleFormChange(ev.target.id, checked, index)}
            />
          );
        },
      },
      {
        accessorKey: "oneTime",
        header: translations.oneTime,
        muiTableHeadCellProps: {
          align: "center",
        },
        muiTableBodyCellProps: {
          align: "center",
        },
        Cell: ({ row }) => {
          const { index, original: rowData } = row;

          if (!rowData.enabled) return null; // hide if not enabled

          return (
            <Checkbox
              id="oneTime"
              checked={rowData.oneTime}
              disabled={!rowData.enabled}
              onChange={(ev, checked) => handleFormChange(ev.target.id, checked, index)}
            />
          );
        },
      },
      {
        accessorKey: "allowedOnProbation",
        header: translations.allowedOnProbation,
        muiTableHeadCellProps: {
          align: "center",
        },
        muiTableBodyCellProps: {
          align: "center",
        },
        size: 250,
        Cell: ({ row }) => {
          const { index, original: rowData } = row;

          if (!rowData.enabled) return null; // hide if not enabled

          return (
            <Checkbox
              id="allowedOnProbation"
              checked={rowData.allowedOnProbation}
              disabled={!rowData.enabled}
              onChange={(ev, checked) => handleFormChange(ev.target.id, checked, index)}
            />
          );
        },
      },
      {
        accessorKey: "allowedOnNotice",
        header: translations.allowedOnNotice,
        muiTableHeadCellProps: {
          align: "center",
        },
        muiTableBodyCellProps: {
          align: "center",
        },
        Cell: ({ row }) => {
          const { index, original: rowData } = row;

          if (!rowData.enabled) return null; // hide if not enabled

          return (
            <Checkbox
              id="allowedOnNotice"
              checked={rowData.allowedOnNotice}
              disabled={!rowData.enabled}
              onChange={(ev, checked) => handleFormChange(ev.target.id, checked, index)}
            />
          );
        },
      },
      // 14) Encashable => hide if not enabled
      ...(isLeaveEncashmentEnabled
        ? [
            {
              accessorKey: "encashable",
              muiTableHeadCellProps: {
                align: "center" as const,
              },
              muiTableBodyCellProps: {
                align: "center" as const,
              },
              header: translations.encashable,
              Cell: ({ row }) => {
                const { index, original: rowData } = row;
                if (!rowData.enabled) return null;
                return (
                  <Checkbox
                    id="encashable"
                    checked={rowData.encashable}
                    disabled={!rowData.enabled}
                    onChange={(ev, checked) => handleFormChange(ev.target.id, checked, index)}
                  />
                );
              },
            },
          ]
        : []),
    ],
    [dropdownOptions, handleFormChange, isLeaveEncashmentEnabled],
  );
};

const AddEditLeavePolicyListItemTable: React.FC<AddEditLeavePolicyListItemProps> = ({
  leaveTypes,
  handleFormChange,
  dropdownOptions,
  isLeaveEncashmentEnabled,
}) => {
  const columns = useLeavePolicyColumns(dropdownOptions, handleFormChange, isLeaveEncashmentEnabled);

  return (
    <DataTable
      columns={columns}
      data={leaveTypes}
      enableColumnPinning
      state={{
        columnPinning: {
          left: ["enabled", "leaveType"], // Pin these columns to the left
        },
      }}
    />
  );
};

export default AddEditLeavePolicyListItemTable;
