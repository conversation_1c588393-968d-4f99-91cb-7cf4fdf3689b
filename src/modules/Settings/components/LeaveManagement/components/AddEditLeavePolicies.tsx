import { <PERSON>, <PERSON><PERSON>, Divider, SelectChangeEvent } from "@mui/material";
import { useMutation, useQueries } from "@tanstack/react-query";
import { format } from "date-fns";
import React, { useCallback, useEffect, useMemo, useState } from "react";
import { useAppDispatch } from "src/customHooks/useAppDispatch";
import { useForm } from "src/customHooks/useForm";
import ContentHeader from "src/modules/Common/ContentHeader/ContentHeader";
import { LeaveAttribute, LeavePolicyDetails } from "src/services/api_definitions/leaveManagement.service";
import leaveManagementService from "src/services/leaveManagement.service";
import masterdataService from "src/services/masterdata.service";
import { setFullviewMode } from "src/store/slices/app.slice";
import { isSubset } from "src/utils/dataUtils";
import AddEditLeavePolicyListItemTable from "./AddEditListItemTable";
import FixedLeavePolicyEditableFields from "./FixedLeavePolicyEditableFields";
import { LeavePoliciyModes, LeavePolicyActionModes } from "./LeavePolicies";

interface AddEditLeavePoliciesProps {
  actionMode: LeavePolicyActionModes;
  setCurrentViewMode: (currentViewMode: LeavePoliciyModes) => void;
  selectedRow: LeavePolicyDetails | null;
  setSelectedRow: (selectedRow: LeavePolicyDetails | null) => void;
}

type DefaultFormState = {
  employeeType: string[];
  name: string;
  startDate: string;
  endDate: string;
  encashmentEnabled: boolean;
  encashmentBasis: string | null;
  maxEncashmentDays: number | null;
};

const translations = {
  name: "Leave Policy Name",
  employeeType: "Employee Type",
  startDate: "Start Month",
  endDate: "End Month",
  addLeavePolicy: "Add Leave Policy",
  editLeavePolicy: "Edit Leave Policy",
  duplicateLeavePolicy: "Duplicate Leave Policy",
  encashmentEnabled: "Enable Leave Encashment",
  encashmentBasis: "Encashment Basis",
  maxEncashmentDays: "Max Encashable Days",
};

const areAccrualDetailsValid = (attribute: LeaveAttribute) => {
  if (!attribute?.allocationMethod) {
    return false;
  }
  if (attribute?.allocationMethod === "Pro-Rated Accrued") {
    return !!attribute.accrualFrequency && attribute?.days && Number(attribute?.days) !== 0;
  }
  return !!attribute?.days && Number(attribute?.days) !== 0;
};

const areCarryForwardDetailsValid = (attribute: LeaveAttribute) =>
  attribute.carryForwardAllowed ? attribute.carryForwardLimit && Number(attribute.carryForwardLimit) !== 0 : true;

const isAutoApproveValid = (attribute: LeaveAttribute) =>
  attribute.autoApproveDays == null ||
  (Number(attribute.autoApproveDays) >= 1 && Number(attribute.autoApproveDays) <= 5);

const areEncashmentDetailsValid = (formDetails: DefaultFormState) => {
  // If encashment is not enabled, validation passes
  if (!formDetails?.encashmentEnabled) {
    return true;
  }
  // If encashment is enabled, both basis and days must be provided
  return !!(
    formDetails?.encashmentBasis &&
    formDetails?.maxEncashmentDays &&
    Number(formDetails?.maxEncashmentDays) > 0
  );
};

const areAnyFieldsDirty = (selectedAttribute: LeaveAttribute, attribute: LeaveAttribute) =>
  selectedAttribute.enabled !== attribute?.enabled ||
  Number(selectedAttribute.carryForwardLimit) !== Number(attribute?.carryForwardLimit) ||
  Number(selectedAttribute.days) !== Number(attribute?.days) ||
  Number(selectedAttribute.maximumAllowed) !== Number(attribute?.maximumAllowed) ||
  Number(selectedAttribute.minimumAllowed) !== Number(attribute?.minimumAllowed) ||
  (selectedAttribute?.allocationMethod === "Pro-Rated Accrued"
    ? selectedAttribute.accrualFrequency !== attribute?.accrualFrequency
    : false) ||
  selectedAttribute.oneTime !== attribute?.oneTime ||
  selectedAttribute.carryForwardAllowed !== attribute?.carryForwardAllowed ||
  selectedAttribute.allocationMethod !== attribute?.allocationMethod ||
  selectedAttribute.halfDayAllowed !== attribute?.halfDayAllowed ||
  selectedAttribute.weekendIncluded !== attribute?.weekendIncluded ||
  selectedAttribute.holidayIncluded !== attribute?.holidayIncluded ||
  selectedAttribute.allowedOnNotice !== attribute?.allowedOnNotice ||
  selectedAttribute.allowedOnProbation !== attribute?.allowedOnProbation ||
  selectedAttribute.encashable !== attribute?.encashable ||
  Number(selectedAttribute.autoApproveDays ?? null) !== Number(attribute?.autoApproveDays ?? null);

const hasValidChangesInRow = (selectedRow: LeavePolicyDetails, selectedAttribute: LeaveAttribute) => {
  const attribute = selectedRow.leaveAttributes.find((attr) => attr.leaveType === selectedAttribute.leaveType);
  if (!attribute) {
    return true;
  }
  return areAnyFieldsDirty(selectedAttribute, attribute);
};

const areSomeLeavesEnabled = (leaveAttributes: LeaveAttribute[]) =>
  leaveAttributes.some((attribute) => attribute.enabled);

const AddEditLeavePolicies: React.FC<AddEditLeavePoliciesProps> = ({
  actionMode,
  selectedRow,
  setCurrentViewMode,
  setSelectedRow,
}) => {
  const isEdit = actionMode === "edit";
  const dispatch = useAppDispatch();
  const [leaveFormState, setLeaveFormState] = useState<LeaveAttribute[]>([]);

  const getDefaultLeaveTypeFormState = useCallback(
    (leaveTypes: string[] = []): LeaveAttribute[] => {
      return (
        (leaveTypes || [])?.map((leaveType) => {
          const selectedRowDetail = selectedRow?.leaveAttributes?.find((leaveAttribute) =>
            leaveType.includes(leaveAttribute?.leaveType as string),
          );
          return {
            carryForwardLimit: selectedRowDetail?.carryForwardLimit || null,
            carryForwardAllowed: selectedRowDetail?.carryForwardAllowed || false,
            days: selectedRowDetail?.days || null,
            enabled: selectedRowDetail?.enabled || false,
            halfDayAllowed: selectedRowDetail?.halfDayAllowed || false,
            holidayIncluded: selectedRowDetail?.holidayIncluded || false,
            leaveType,
            maximumAllowed: selectedRowDetail?.maximumAllowed || null,
            minimumAllowed: selectedRowDetail?.minimumAllowed || null,
            oneTime: selectedRowDetail?.oneTime || false,
            accrualFrequency: selectedRowDetail?.accrualFrequency || null,
            weekendIncluded: selectedRowDetail?.weekendIncluded || false,
            allowedOnProbation: selectedRowDetail?.allowedOnProbation || false,
            allowedOnNotice: selectedRowDetail?.allowedOnNotice || false,
            encashable: selectedRowDetail?.encashable || false,
            allocationMethod: selectedRowDetail?.allocationMethod || null,
            autoApproveDays: selectedRowDetail?.autoApproveDays ?? null,
          };
        }) || []
      );
    },
    [selectedRow],
  );

  const [employeeTypes, leaveTypes, proRataFrequencies, allocationMethods] = useQueries({
    queries: [
      {
        queryKey: ["get-employee-types"],
        queryFn: async () => masterdataService.getACLs("EmployeeType"),
        refetchOnWindowFocus: false,
      },
      {
        queryKey: ["get-leave-type-details"],
        queryFn: async () => {
          const resp = await leaveManagementService.getLeaveDetails();
          const defaultLeaves = resp?.default_leaves || [];
          const customLeaves = resp?.custom_leaves || [];
          return [...defaultLeaves.map((leave) => leave.type), ...customLeaves.map((leave) => leave?.type)];
        },
        refetchOnWindowFocus: false,
      },
      {
        queryKey: ["get-frequency-types"],
        queryFn: async () => masterdataService.getACLs<string>("FrequencyType"),
        refetchOnWindowFocus: false,
      },
      {
        queryKey: ["get-allocation-methods"],
        queryFn: async () => masterdataService.getACLs<string>("LeaveAllocationMethod"),
        refetchOnWindowFocus: false,
      },
    ],
  });

  const createLeavePolicy = useMutation({
    mutationKey: ["create-leave-policy"],
    mutationFn: () =>
      leaveManagementService.createLeavePolicies({
        employee_types: typedFormDetails?.employeeType,
        name: typedFormDetails?.name,
        start_date: format(typedFormDetails?.startDate, "yyyy-MM-dd"),
        end_date: format(typedFormDetails?.endDate, "yyyy-MM-dd"),
        encashment_enabled: typedFormDetails?.encashmentEnabled,
        encashment_basis: typedFormDetails?.encashmentBasis,
        max_encashment_days: typedFormDetails?.maxEncashmentDays,
        leave_attributes: leaveFormState
          ?.filter((leaveAttribute) => leaveAttribute?.enabled)
          ?.map((leaveAttribute) => ({
            carry_forward_limit: leaveAttribute?.carryForwardLimit,
            half_day_allowed: leaveAttribute?.halfDayAllowed,
            holiday_included: leaveAttribute?.holidayIncluded,
            leave_type: leaveAttribute?.leaveType,
            maximum_allowed: leaveAttribute?.maximumAllowed,
            minimum_allowed: leaveAttribute?.minimumAllowed,
            one_time: leaveAttribute?.oneTime,
            accrual_frequency: leaveAttribute?.accrualFrequency,
            weekend_included: leaveAttribute?.weekendIncluded,
            enabled: leaveAttribute?.enabled,
            carry_forward_allowed: leaveAttribute?.carryForwardAllowed,
            days: leaveAttribute?.days,
            allowed_on_probation: leaveAttribute?.allowedOnProbation,
            allowed_on_notice: leaveAttribute?.allowedOnNotice,
            encashable: leaveAttribute?.encashable,
            allocation_method: leaveAttribute?.allocationMethod,
            auto_approve_days: leaveAttribute?.autoApproveDays ?? null,
          })),
      }),
    onSuccess: () => {
      dispatch(setFullviewMode(false));
      setCurrentViewMode(LeavePoliciyModes.VIEW_LEAVE_POLICIES);
      setLeaveFormState([]);
    },
  });

  const updateLeavePolicy = useMutation({
    mutationKey: ["update-leave-policy"],
    mutationFn: () =>
      leaveManagementService.updateLeavePolicies({
        employee_types: typedFormDetails?.employeeType,
        name: selectedRow?.name as string,
        start_date: format(typedFormDetails?.startDate, "yyyy-MM-dd"),
        end_date: format(typedFormDetails?.endDate, "yyyy-MM-dd"),
        encashment_enabled: typedFormDetails?.encashmentEnabled,
        encashment_basis: typedFormDetails?.encashmentBasis,
        max_encashment_days: typedFormDetails?.maxEncashmentDays,
        leave_attributes: leaveFormState?.map((leaveAttribute) => ({
          carry_forward_limit: leaveAttribute?.carryForwardLimit,
          half_day_allowed: leaveAttribute?.halfDayAllowed,
          holiday_included: leaveAttribute?.holidayIncluded,
          leave_type: leaveAttribute?.leaveType,
          maximum_allowed: leaveAttribute?.maximumAllowed,
          minimum_allowed: leaveAttribute?.minimumAllowed,
          one_time: leaveAttribute?.oneTime,
          accrual_frequency: leaveAttribute?.accrualFrequency,
          weekend_included: leaveAttribute?.weekendIncluded,
          enabled: leaveAttribute?.enabled,
          carry_forward_allowed: leaveAttribute?.carryForwardAllowed,
          days: leaveAttribute?.days,
          allowed_on_probation: leaveAttribute?.allowedOnProbation,
          allowed_on_notice: leaveAttribute?.allowedOnNotice,
          encashable: leaveAttribute?.encashable,
          allocation_method: leaveAttribute?.allocationMethod,
          auto_approve_days: leaveAttribute?.autoApproveDays ?? null,
        })),
        new_name: typedFormDetails?.name,
      }),
    onSuccess: () => {
      dispatch(setFullviewMode(false));
      setCurrentViewMode(LeavePoliciyModes.VIEW_LEAVE_POLICIES);
      setLeaveFormState([]);
      setSelectedRow(null);
    },
  });

  const defaultFormState: DefaultFormState = useMemo(() => {
    const isDuplicate = actionMode === "duplicate";
    return {
      name: isDuplicate ? "" : selectedRow?.name || "",
      employeeType: isDuplicate ? [] : selectedRow?.employeeTypes || [],
      startDate: isDuplicate ? "" : selectedRow?.startDate || "",
      endDate: isDuplicate ? "" : selectedRow?.endDate || "",
      encashmentEnabled: isDuplicate ? false : selectedRow?.encashmentEnabled || false,
      encashmentBasis: isDuplicate ? null : selectedRow?.encashmentBasis || null,
      maxEncashmentDays: isDuplicate ? null : selectedRow?.maxEncashmentDays || null,
    };
  }, [selectedRow, actionMode]);

  const { formDetails, formErrors, handleChange, setFormDetail } = useForm({
    initialState: defaultFormState,
    isBulk: false,
    validations: {
      employeeType: [],
      endDate: [],
      startDate: [],
      name: [],
      encashmentEnabled: [],
      encashmentBasis: [],
      maxEncashmentDays: [],
    },
  });

  useEffect(() => {
    const leaveState = getDefaultLeaveTypeFormState(leaveTypes?.data || []);
    setLeaveFormState(leaveState.sort((a, b) => (a?.enabled === b?.enabled ? 0 : a?.enabled ? -1 : 1)));
  }, [leaveTypes?.data]);

  const typedFormDetails = formDetails as DefaultFormState;
  const typedFormErrors = formErrors as Record<keyof DefaultFormState, string>;

  const onBackClick = () => {
    dispatch(setFullviewMode(false));
    setSelectedRow(null);
    setCurrentViewMode(LeavePoliciyModes.VIEW_LEAVE_POLICIES);
  };

  const handleCheckboxChange = (ev: SelectChangeEvent<string[]>) => {
    const { value } = ev.target;
    setFormDetail("employeeType", value);
  };

  const handleFormChange = (key: string, value: unknown, index: number) => {
    if (key === "carryForwardAllowed") {
      setLeaveFormState((prevState) => {
        const updatedLeaveTypes = [...prevState];
        updatedLeaveTypes[index] = {
          ...updatedLeaveTypes[index],
          [key]: value as boolean,
          carryForwardLimit: value ? updatedLeaveTypes[index].carryForwardLimit : ("" as unknown as number),
        };
        return updatedLeaveTypes;
      });
      return;
    }

    if (key === "allocationMethod") {
      setLeaveFormState((prevState) => {
        const updatedLeaveTypes = [...prevState];
        updatedLeaveTypes[index] = {
          ...updatedLeaveTypes[index],
          [key]: value as string,
          accrualFrequency: null,
        };
        return updatedLeaveTypes;
      });
      return;
    }

    setLeaveFormState((prevState) => {
      const updatedLeaveTypes = [...prevState];
      updatedLeaveTypes[index] = {
        ...updatedLeaveTypes[index],
        [key]: value,
      };
      return updatedLeaveTypes;
    });
  };

  const isCreateEnabled = useCallback((): boolean => {
    if (!areSomeLeavesEnabled(leaveFormState)) {
      return false;
    }
    const hasBasicDependentValidationsPassed = leaveFormState.every((attribute) => {
      if (!attribute.enabled) return true;
      return (
        areCarryForwardDetailsValid(attribute) && areAccrualDetailsValid(attribute) && isAutoApproveValid(attribute)
      );
    });

    return (
      typedFormDetails.employeeType.length !== 0 &&
      !!typedFormDetails.endDate &&
      !!typedFormDetails.startDate &&
      !!typedFormDetails.name &&
      hasBasicDependentValidationsPassed &&
      areEncashmentDetailsValid(typedFormDetails)
    );
  }, [leaveFormState, typedFormDetails]);

  const isUpdateEnabled = useCallback(
    (selectedLeavePolicyDetails: LeavePolicyDetails) => {
      if (!areSomeLeavesEnabled(leaveFormState)) {
        return false;
      }

      const haveBasicFormDetailsChanged =
        (typedFormDetails?.name != null && selectedLeavePolicyDetails?.name !== typedFormDetails?.name) ||
        (typedFormDetails.employeeType?.some(
          (employeeType) => !selectedLeavePolicyDetails.employeeTypes?.includes(employeeType),
        ) &&
          typedFormDetails?.employeeType?.length !== 0) ||
        (typedFormDetails?.startDate !== null &&
          selectedLeavePolicyDetails?.startDate !== typedFormDetails?.startDate) ||
        (typedFormDetails?.endDate !== null && selectedLeavePolicyDetails?.endDate !== typedFormDetails?.endDate) ||
        selectedLeavePolicyDetails?.encashmentEnabled !== typedFormDetails?.encashmentEnabled ||
        selectedLeavePolicyDetails?.encashmentBasis !== typedFormDetails?.encashmentBasis ||
        selectedLeavePolicyDetails?.maxEncashmentDays !== typedFormDetails?.maxEncashmentDays;

      if (haveBasicFormDetailsChanged) {
        return true;
      }

      const enabledLeaveAttributes = leaveFormState?.filter((eachLeaveFromState) => eachLeaveFromState.enabled);
      const enabledAttributeKeys = enabledLeaveAttributes?.map((eachAttribute) => eachAttribute?.leaveType || "") || [];
      const previouslySelectedKeys =
        selectedLeavePolicyDetails?.leaveAttributes?.map((eachAttribute) => eachAttribute?.leaveType || "") || [];
      const hasAnyLeaveTypeBeenDisabled = !isSubset(previouslySelectedKeys, enabledAttributeKeys);

      if (hasAnyLeaveTypeBeenDisabled) {
        return true;
      }

      const hasBasicDependentValidationsPassed = enabledLeaveAttributes?.every(
        (selectedAttribute) =>
          areAccrualDetailsValid(selectedAttribute) && areCarryForwardDetailsValid(selectedAttribute),
      );

      if (!hasBasicDependentValidationsPassed || !areEncashmentDetailsValid(typedFormDetails)) {
        return false;
      }

      const hasAnyRowFieldsChanged = enabledLeaveAttributes?.some((selectedAttribute) =>
        hasValidChangesInRow(selectedLeavePolicyDetails, selectedAttribute),
      );

      return hasAnyRowFieldsChanged;
    },
    [typedFormDetails, leaveFormState],
  );

  const onUpdate = () => {
    updateLeavePolicy.mutate();
  };

  const onCreate = () => {
    createLeavePolicy.mutate();
  };

  const headerTitle = useMemo(() => {
    if (actionMode === "duplicate") {
      return `Duplicate ${selectedRow?.name}`;
    }
    if (isEdit) {
      return translations.editLeavePolicy;
    }
    return translations.addLeavePolicy;
  }, [actionMode, isEdit, selectedRow]);

  return (
    <Box display="flex" flexDirection="column" gap={1} position="relative" overflow="hidden">
      <Box display="flex" flexDirection="column" sx={{ width: "100%" }} gap={2} margin={1}>
        <ContentHeader showBackButton goBack={onBackClick} title={headerTitle} />
        <Divider />
      </Box>
      <FixedLeavePolicyEditableFields
        employeeTypes={employeeTypes}
        formDetails={formDetails}
        formErrors={formErrors}
        handleChange={handleChange}
        setFormDetail={setFormDetail}
        handleCheckboxChange={handleCheckboxChange}
        translations={translations}
        typedFormErrors={typedFormErrors}
        typedFormDetails={typedFormDetails}
        isLeaveEncashmentEnabled={typedFormDetails?.encashmentEnabled}
      />
      <Box
        display="flex"
        flexDirection="column"
        gap={2}
        sx={{
          maxHeight: window.innerHeight - 400,
          overflow: "scroll",
          marginTop: "4px",
        }}
      >
        <AddEditLeavePolicyListItemTable
          dropdownOptions={{
            accrualFrequency: proRataFrequencies?.data || [],
            allocationMethod: allocationMethods?.data || [],
          }}
          handleFormChange={(key, value, index) => handleFormChange(key, value, index)}
          leaveTypes={leaveFormState}
          isLeaveEncashmentEnabled={typedFormDetails?.encashmentEnabled}
        />
      </Box>
      <Box
        position="fixed"
        bottom={0}
        right={0}
        padding={4}
        display="flex"
        justifyContent="flex-end"
        zIndex={2}
        width="object-fit"
      >
        <Button
          disabled={isEdit && selectedRow ? !isUpdateEnabled(selectedRow) : !isCreateEnabled()}
          variant="contained"
          onClick={() => (selectedRow && isEdit ? onUpdate() : onCreate())}
        >
          {selectedRow && isEdit ? "Save" : "Create"}
        </Button>
      </Box>
    </Box>
  );
};

export default AddEditLeavePolicies;
