// src/hooks/useChatMutation.ts
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useAppSelector } from "./useAppSelector";

// Define a type for the token data
type TokenData = {
  tokens: string[];
  chat_id: string;
};

// src/utils/consumeSSE.ts
export async function consumeSSE(
  url: string,
  body: unknown,
  onToken: (tok: string, chatId: string) => void,
  signal?: AbortSignal,
) {
  const res = await fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Accept: "text/event-stream",
    },
    body: JSON.stringify(body),
    signal,
  });
  if (!res.ok || !res.body) throw new Error("Network error");

  const reader = res.body
    .pipeThrough(new TextDecoderStream()) // decode UTF-8
    .getReader();

  let buffer = "";
  while (true) {
    const { value, done } = await reader.read();
    if (done) break;

    buffer += value;
    const frames = buffer.split("\n\n");
    buffer = frames.pop()!; // keep partial frame

    for (const f of frames) {
      if (!f.startsWith("data:")) {
        console.log("Non-data frame:", f);
        continue;
      }

      const payload = f.slice(5).trim();
      console.log("Raw payload:", payload);

      if (payload === "[DONE]") {
        console.log("Received [DONE] signal");
        return;
      }

      try {
        // Parse the payload
        const parsedData = JSON.parse(payload);
        console.log("Parsed data:", parsedData);

        // Check what fields are available in the parsed data
        console.log("Available fields:", Object.keys(parsedData));

        // Extract delta and chat_id, with fallbacks
        const delta = parsedData.delta || parsedData.content || parsedData.text || "";
        const chat_id = parsedData.chat_id || parsedData.chatId || parsedData.id || "unknown";

        console.log("Extracted delta:", delta, "chat_id:", chat_id);

        onToken(delta, chat_id);
      } catch (error) {
        console.error("Error parsing SSE payload:", error, "Payload:", payload);
      }
    }
  }
}

export function useChatMutation() {
  const qc = useQueryClient();
  const { selectedRole, selectedOrganisation } = useAppSelector((state) => state.userManagement);

  return useMutation<void, Error, { prompt: string; chatId: string | null }>({
    mutationFn: async ({ prompt, chatId }) => {
      const controller = new AbortController();

      await consumeSSE(
        process.env.CHAT_API_URL || "",
        {
          query: prompt,
          chat_id: chatId,
          organisation_id: selectedOrganisation,
          role_id: selectedRole,
        },
        (token, newId) => {
          console.log("Callback received token:", token, "newId:", newId);

          // Use the existing chatId if newId is not provided
          const effectiveId = newId || "new";
          console.log("Using effective ID for cache:", effectiveId);

          // Store both the token and chat_id in the cache
          qc.setQueryData<TokenData>(["chatTokens", effectiveId], (prev = { tokens: [], chat_id: newId || "" }) => {
            console.log("Previous data in cache:", prev);
            return {
              tokens: [...prev.tokens, token],
              chat_id: newId || prev.chat_id,
            };
          });

          // If we have a new chat ID, also update the "new" key for initial fetch
          if (newId && effectiveId !== "new") {
            qc.setQueryData<TokenData>(["chatTokens", "new"], (prev = { tokens: [], chat_id: "" }) => ({
              tokens: prev.tokens,
              chat_id: newId,
            }));
          }
        },
        controller.signal,
      );
    },
  });
}
